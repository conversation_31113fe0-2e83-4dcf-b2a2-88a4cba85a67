<div class="ava-toast ava-toast-warning"
     [style.width]="customWidth"
     [style.height]="customHeight">
  
  <!-- Icon -->
  <div class="ava-toast-icon">
    <ava-icon 
      [iconName]="icon || 'alert-triangle'" 
      [iconColor]="iconColor || 'white'" 
      [iconSize]="24">
    </ava-icon>
  </div>

  <!-- Content -->
  <div class="ava-toast-content">
    <div class="ava-toast-title" *ngIf="title">{{ title }}</div>
    <div class="ava-toast-message" *ngIf="message">{{ message }}</div>
    <ng-content></ng-content>
    
    <!-- Action Button -->
    <div class="ava-toast-actions" *ngIf="showActionButton">
      <ava-button
        [label]="actionButtonText || 'Take Action'"
        variant="secondary"
        size="small"
        (userClick)="onAction()">
      </ava-button>
    </div>
  </div>

  <!-- Close Button -->
  <ava-button
    *ngIf="showCloseButton"
    class="ava-toast-close"
    size="small"
    iconName="x"
    iconColor="white"
    [iconSize]="16"
    iconPosition="only"
    (userClick)="onClose()"
    aria-label="Close toast">
  </ava-button>

  <!-- Progress Bar -->
  <div class="ava-toast-progress"
       *ngIf="showProgress && duration && duration > 0"
       [style.animation-duration.ms]="duration">
  </div>
</div>
