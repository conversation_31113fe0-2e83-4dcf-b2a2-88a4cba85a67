import { Component, ViewChild, ViewContainerRef, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastPosition } from '../toast.service';

@Component({
  selector: 'ava-toast-container',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="ava-toast-container" [ngClass]="positionClass">
      <ng-container #container></ng-container>
    </div>
  `,
  styles: [`
    .ava-toast-container {
      position: fixed;
      z-index: 10000;
      pointer-events: none;
      display: flex;
      flex-direction: column;
      gap: 8px;
      max-width: 400px;
      width: 100%;
    }

    .ava-toast-container.top-left {
      top: 20px;
      left: 20px;
    }

    .ava-toast-container.top-center {
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .ava-toast-container.top-right {
      top: 20px;
      right: 20px;
    }

    .ava-toast-container.bottom-left {
      bottom: 20px;
      left: 20px;
    }

    .ava-toast-container.bottom-center {
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .ava-toast-container.bottom-right {
      bottom: 20px;
      right: 20px;
    }

    @media (max-width: 768px) {
      .ava-toast-container {
        max-width: calc(100vw - 40px);
        left: 20px !important;
        right: 20px !important;
        transform: none !important;
      }
    }
  `]
})
export class ToastContainerComponent {
  @ViewChild('container', { read: ViewContainerRef, static: true }) 
  container!: ViewContainerRef;

  positionClass = 'top-right';

  setPosition(position: ToastPosition) {
    this.positionClass = position;
  }
}
