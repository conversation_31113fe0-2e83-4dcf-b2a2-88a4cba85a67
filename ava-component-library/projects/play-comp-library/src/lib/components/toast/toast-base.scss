/* Toast Base Styles - Uses only _token.css variables */

.ava-toast {
  pointer-events: auto;
  display: flex;
  align-items: center;
  gap: var(--global-spacing-3);
  padding: var(--toast-padding);
  border-radius: var(--toast-border-radius);
  box-shadow: var(--toast-shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(100px);
  opacity: 0;
  max-width: 100%;
  min-width: var(--toast-min-width);
  position: relative;
  overflow: hidden;
  font-family: var(--global-font-family);
}

.ava-toast.ava-toast-show {
  transform: translateY(0);
  opacity: 1;
}

.ava-toast.toast-hide {
  transform: translateY(-100px);
  opacity: 0;
  margin-top: -80px;
}

/* Toast Icon */
.ava-toast-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Toast Content */
.ava-toast-content {
  flex: 1;
  min-width: 0;
}

.ava-toast-title {
  font: var(--toast-title-font);
  font-weight: var(--toast-title-weight);
  line-height: var(--toast-title-line-height);
  color: inherit;
}

.ava-toast-message {
  font: var(--toast-message-font);
  font-weight: var(--toast-message-weight);
  line-height: var(--toast-message-line-height);
  opacity: 0.9;
  color: inherit;
}

.ava-toast-custom-content {
  margin-top: var(--global-spacing-2);
}

/* Close Button */
.ava-toast-close {
  background: none;
  border: none;
  color: currentColor;
  cursor: pointer;
  padding: var(--global-spacing-1);
  border-radius: var(--global-radius-sm);
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ava-toast-close:hover {
  opacity: 1;
}

.ava-toast-close:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Progress Bar */
.ava-toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 0 var(--toast-border-radius) var(--toast-border-radius);
  animation: toast-progress linear forwards;
  width: 0;
}

@keyframes toast-progress {
  to {
    width: 100%;
  }
}

/* Action Buttons */
.ava-toast-actions {
  display: flex;
  gap: var(--global-spacing-2);
  margin-top: var(--global-spacing-3);
}

.ava-toast-action {
  padding: var(--global-spacing-1) var(--global-spacing-3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: currentColor;
  border-radius: var(--global-radius-sm);
  font-size: var(--global-font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--global-font-family);
  font-weight: var(--global-font-weight-medium);
}

.ava-toast-action:hover {
  background: rgba(255, 255, 255, 0.2);
}

.ava-toast-action:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Toast Type Variants */
.ava-toast-success {
  background: var(--toast-success-background);
  color: var(--toast-success-text);
  border-color: var(--toast-success-border);
}

.ava-toast-error {
  background: var(--toast-error-background);
  color: var(--toast-error-text);
  border-color: var(--toast-error-border);
}

.ava-toast-warning {
  background: var(--toast-warning-background);
  color: var(--toast-warning-text);
  border-color: var(--toast-warning-border);
}

.ava-toast-info {
  background: var(--toast-info-background);
  color: var(--toast-info-text);
  border-color: var(--toast-info-border);
}

.ava-toast-custom {
  background: var(--toast-background);
  color: var(--color-text-primary);
  border: 1px solid var(--toast-border);
}

/* Warning toast specific styles for black text */
.ava-toast-warning .ava-toast-action {
  border-color: rgba(0, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
}

.ava-toast-warning .ava-toast-action:hover {
  background: rgba(0, 0, 0, 0.2);
}

.ava-toast-warning .ava-toast-close:focus,
.ava-toast-warning .ava-toast-action:focus {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ava-toast {
    min-width: auto;
    width: 100%;
    max-width: calc(100vw - 40px);
  }
  
  .ava-toast-content {
    font-size: var(--global-font-size-sm);
  }
  
  .ava-toast-title {
    font-size: var(--global-font-size-base);
  }
}
