import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ToastService, ToastPosition, CustomAction } from '../../../../../play-comp-library/src/lib/components/toast';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'app-toast-documentation',
  standalone: true,
  imports: [CommonModule, FormsModule, ButtonComponent],
  templateUrl: './app-toast.component.html',
  styleUrls: ['./app-toast.component.scss']
})
export class ToastDocumentationComponent implements OnInit {

  constructor(private toastService: ToastService) {}

  ngOnInit() {
    // Set initial position to top-right
    this.toastService.setPosition('top-right');
  }

  // 1. Basic Toast Types
  showBasicSuccess() {
    this.toastService.success({
      title: 'Success!',
      message: 'Operation completed successfully.',
      duration: 4000
    });
  }

  showBasicError() {
    this.toastService.error({
      title: 'Error!',
      message: 'Something went wrong. Please try again.',
      duration: 4000
    });
  }

  showBasicWarning() {
    this.toastService.warning({
      title: 'Warning!',
      message: 'Please review your input carefully.',
      duration: 4000
    });
  }

  showBasicInfo() {
    this.toastService.info({
      title: 'Information',
      message: 'Here is some important information for you.',
      duration: 4000
    });
  }

  showBasicDefault() {
    this.toastService.default({
      title: 'Default Toast',
      message: 'This is a default toast with white background and black text.',
      icon: 'bell',
      iconColor: 'var(--color-text-primary)',
      duration: 4000
    });
  }

  // 2. Toast Types with Action Buttons
  showErrorWithRetry() {
    this.toastService.error({
      title: 'Connection Failed',
      message: 'Unable to connect to server. Would you like to retry?',
      showRetryButton: true,
      retryButtonText: 'Retry Connection',
      duration: 6000
    }).then(result => {
      if (result.action === 'retry') {
        this.toastService.success({
          title: 'Retrying...',
          message: 'Attempting to reconnect to server.',
          duration: 3000
        });
      }
    });
  }

  showWarningWithAction() {
    this.toastService.warning({
      title: 'Unsaved Changes',
      message: 'You have unsaved changes. Do you want to save them?',
      showActionButton: true,
      actionButtonText: 'Save Changes',
      duration: 8000
    }).then(result => {
      if (result.action === 'action') {
        this.toastService.success({
          title: 'Changes Saved',
          message: 'Your changes have been saved successfully.',
          duration: 3000
        });
      }
    });
  }

  showInfoWithLearnMore() {
    this.toastService.info({
      title: 'New Feature Available',
      message: 'We have added new features to improve your experience.',
      showLearnMoreButton: true,
      learnMoreButtonText: 'Learn More',
      duration: 6000
    }).then(result => {
      if (result.action === 'learn-more') {
        this.toastService.info({
          title: 'Feature Documentation',
          message: 'Opening feature documentation...',
          duration: 3000
        });
      }
    });
  }

  // 3. Position Examples
  showPositionExample(position: ToastPosition) {
    this.toastService.setPosition(position);
    this.toastService.info({
      title: `Toast Position: ${position}`,
      message: `This toast is displayed at ${position} position.`,
      duration: 4000
    });
  }

  // 4. Custom Toast Showcases
  showGradientToast() {
    this.toastService.custom({
      title: '🎉 Gradient Toast',
      message: 'Beautiful gradient background with custom styling!',
      customBackground: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      customTextColor: 'white',
      icon: 'sparkles',
      iconColor: 'white',
      customWidth: '420px',
      showCustomActions: true,
      customActions: [
        { text: 'Awesome!', variant: 'secondary', action: 'awesome' }
      ],
      duration: 5000
    }).then(result => {
      if (result.action === 'awesome') {
        this.toastService.success({
          title: 'Thanks!',
          message: 'Glad you like our gradient toast!',
          duration: 3000
        });
      }
    });
  }

  showDarkToast() {
    this.toastService.custom({
      title: '🌙 Dark Mode Toast',
      message: 'Sleek dark theme with custom actions and styling.',
      customBackground: '#1a1a1a',
      customTextColor: '#ffffff',
      progressColor: '#3b82f6',
      icon: 'moon',
      iconColor: '#3b82f6',
      showCustomActions: true,
      customActions: [
        { text: 'Settings', variant: 'secondary', action: 'settings' },
        { text: 'Dismiss', variant: 'default', action: 'dismiss' }
      ],
      customWidth: '450px',
      duration: 6000
    }).then(result => {
      if (result.action === 'settings') {
        this.toastService.info({
          title: 'Settings',
          message: 'Opening settings panel...',
          duration: 2000
        });
      }
    });
  }

  showBrandToast() {
    this.toastService.custom({
      title: '🚀 Brand Toast',
      message: 'Custom branded toast with company colors and style.',
      customBackground: '#6366f1',
      customTextColor: 'white',
      progressColor: '#a5b4fc',
      icon: 'rocket',
      iconColor: 'white',
      customWidth: '400px',
      duration: 5000
    });
  }

  showCompactToast() {
    this.toastService.custom({
      title: '📱 Compact',
      message: 'Small and efficient.',
      customWidth: '280px',
      customHeight: '60px',
      icon: 'smartphone',
      duration: 3000
    });
  }

  showCustomContentToast() {
    this.toastService.custom({
      title: '🎨 Custom Content',
      message: 'This toast demonstrates custom content and styling capabilities.',
      customBackground: '#f59e0b',
      customTextColor: 'white',
      icon: 'palette',
      iconColor: 'white',
      customWidth: '450px',
      showCustomActions: true,
      customActions: [
        { text: 'Customize', variant: 'secondary', action: 'customize' },
        { text: 'Preview', variant: 'default', action: 'preview' }
      ],
      duration: 7000
    }).then(result => {
      if (result.action === 'customize') {
        this.toastService.info({
          title: 'Customization',
          message: 'Opening customization panel...',
          duration: 2000
        });
      } else if (result.action === 'preview') {
        this.toastService.info({
          title: 'Preview Mode',
          message: 'Entering preview mode...',
          duration: 2000
        });
      }
    });
  }

  // 5. Configuration Options
  showDurationExample(duration: number) {
    const durationText = duration === 2000 ? 'Quick (2s)' :
                        duration === 4000 ? 'Normal (4s)' : 'Long (8s)';

    this.toastService.info({
      title: `Duration Example: ${durationText}`,
      message: `This toast will auto-dismiss after ${duration/1000} seconds.`,
      duration: duration
    });
  }

  showSizeExample(width: string, height: string) {
    const sizeText = width === '300px' ? 'Compact' :
                    width === '400px' ? 'Normal' : 'Large';

    this.toastService.custom({
      title: `${sizeText} Size Toast`,
      message: `This toast demonstrates ${sizeText.toLowerCase()} sizing with ${width} width and ${height} height.`,
      customWidth: width,
      customHeight: height,
      icon: 'maximize-2',
      duration: 4000
    });
  }

  showProgressExample(showProgress: boolean) {
    this.toastService.success({
      title: showProgress ? 'With Progress Bar' : 'Without Progress Bar',
      message: `This toast ${showProgress ? 'shows' : 'hides'} the progress bar indicator.`,
      showProgress: showProgress,
      duration: 5000
    });
  }

  // 6. Actions
  dismissAllToasts() {
    this.toastService.dismissAll();

    // Show confirmation after a brief delay
    setTimeout(() => {
      this.toastService.info({
        title: 'All Toasts Dismissed',
        message: 'All active toast notifications have been cleared.',
        duration: 3000
      });
    }, 500);
  }
}
